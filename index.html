<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>背单词小程序</title>
    <style>
        body { font-family: Arial, sans-serif; background: #f5f5f5; margin: 0; padding: 0; }
        .container { max-width: 400px; margin: 40px auto; background: #fff; border-radius: 8px; box-shadow: 0 2px 8px #ccc; padding: 24px; }
        h2 { text-align: center; }
        textarea { width: 100%; height: 80px; margin-bottom: 12px; }
        button { padding: 8px 16px; margin: 8px 0; }
        .question { font-size: 1.2em; margin: 20px 0; text-align: center; }
        .result { font-weight: bold; margin: 10px 0; text-align: center; }
        .score { margin: 10px 0; text-align: center; }
        input[type="text"] { width: 100%; padding: 8px; font-size: 1em; }
        /* 对对碰部分样式增强 */
        #match-flex { display: flex; justify-content: space-between; align-items: stretch; position: relative; }
        #match-flex > div { width: 45%; display: flex; flex-direction: column; height: 100%; }
        #match-cn, #match-en { flex: 1 1 auto; height: 100%; }
    </style>
</head>
<body>
<div class="container">
    <h2>背单词小程序</h2>
    <div id="setup">
        <p>请输入中英文对照单词列表（每行一个，格式：中文 英文 或 中文,英文）：</p>
        <textarea id="wordlist" placeholder="例如：\n苹果 apple\n香蕉 banana\n...\n"></textarea>
        <button onclick="startQuiz()">开始背单词</button>
        <button onclick="startChoiceQuiz()">开始选择题</button>
    </div>
    <div id="wordlist-manager" style="margin-bottom:16px;">
        <label>词表名称：</label>
        <input type="text" id="wordlist-name" style="width:120px;">
        <button onclick="saveWordlist()">保存词表</button>
        <button onclick="newWordlist()">新建词表</button>
        <select id="wordlist-select" onchange="loadWordlist()" style="margin-left:8px;"></select>
        <button onclick="deleteWordlist()">删除词表</button>
        <button onclick="exportWordlists()">导出词表</button>
        <input type="file" id="import-file" style="display:none" accept="application/json" onchange="importWordlists(event)">
        <button onclick="document.getElementById('import-file').click()">导入词表</button>
    </div>
    <div id="quiz" style="display:none;">
        <div class="question" id="question"></div>
        <input type="text" id="answer" placeholder="请输入英文单词" onkeydown="if(event.key==='Enter'){checkAnswer();}">
        <button onclick="checkAnswer()">提交</button>
        <div class="result" id="result"></div>
        <div class="score" id="score"></div>
        <button onclick="nextWord()" id="nextBtn" style="display:none;">下一个</button>
        <button onclick="restart()">重新开始</button>
    </div>
    <div id="choice-quiz" style="display:none;">
        <div class="question" id="choice-question"></div>
        <div id="choice-options" style="margin: 20px 0; display: flex; flex-direction: column; gap: 12px;"></div>
        <div class="result" id="choice-result"></div>
        <div class="score" id="choice-score"></div>
        <button onclick="nextChoice()" id="choiceNextBtn" style="display:none;">下一个</button>
        <button onclick="restart()">重新开始</button>
    </div>
</div>
<script>
let words = [];
let current = null;
let correct = 0, wrong = 0, total = 0;
let usedIndexes = [];

// 选择题模式
let choiceWords = [];
let choiceUsed = [];
let choiceCorrect = 0, choiceWrong = 0, choiceTotal = 0;
let choiceCurrent = null;

function shuffle(arr) {
    for (let i = arr.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [arr[i], arr[j]] = [arr[j], arr[i]];
    }
}

function startQuiz() {
    const list = document.getElementById('wordlist').value.trim().split('\n');
    words = [];
    for (let line of list) {
        let cn, en;
        if (line.includes(',')) {
            [cn, en] = line.split(',');
        } else {
            const idx = line.indexOf(' ');
            if (idx > 0) {
                cn = line.slice(0, idx);
                en = line.slice(idx + 1);
            }
        }
        if (cn && en) words.push({cn: cn.trim(), en: en.trim()});
    }
    if (words.length === 0) {
        alert('请正确输入单词列表！');
        return;
    }
    correct = 0; wrong = 0; total = 0; usedIndexes = [];
    document.getElementById('setup').style.display = 'none';
    document.getElementById('quiz').style.display = '';
    nextWord();
}

function nextWord() {
    document.getElementById('result').textContent = '';
    document.getElementById('answer').value = '';
    document.getElementById('answer').disabled = false;
    document.getElementById('nextBtn').style.display = 'none';
    if (usedIndexes.length === words.length) {
        document.getElementById('question').textContent = '全部完成！';
        document.getElementById('score').textContent = `总共${total}题，答对${correct}题，答错${wrong}题。`;
        document.getElementById('answer').style.display = 'none';
        return;
    }
    let idx;
    do {
        idx = Math.floor(Math.random() * words.length);
    } while (usedIndexes.includes(idx));
    usedIndexes.push(idx);
    current = words[idx];
    document.getElementById('question').textContent = `中文：${current.cn}`;
    document.getElementById('score').textContent = `已答${total}题，答对${correct}题，答错${wrong}题。`;
    document.getElementById('answer').style.display = '';
    document.getElementById('answer').focus();
}

function checkAnswer() {
    const ans = document.getElementById('answer').value.trim();
    if (!ans) return;
    total++;
    if (ans.toLowerCase() === current.en.toLowerCase()) {
        correct++;
        document.getElementById('result').textContent = '答对了！';
        document.getElementById('result').style.color = 'green';
        document.getElementById('answer').disabled = true;
        document.getElementById('nextBtn').style.display = '';
    } else {
        wrong++;
        document.getElementById('result').textContent = `答错了，正确答案是：${current.en}`;
        document.getElementById('result').style.color = 'red';
        document.getElementById('answer').focus();
        document.getElementById('nextBtn').style.display = '';
    }
    document.getElementById('score').textContent = `已答${total}题，答对${correct}题，答错${wrong}题。`;
}

function restart() {
    document.getElementById('setup').style.display = '';
    document.getElementById('quiz').style.display = 'none';
    document.getElementById('wordlist').focus();
}

function getWordlistStore() {
    return JSON.parse(localStorage.getItem('wordlists') || '{}');
}
function setWordlistStore(obj) {
    localStorage.setItem('wordlists', JSON.stringify(obj));
}
function refreshWordlistSelect() {
    const select = document.getElementById('wordlist-select');
    const store = getWordlistStore();
    select.innerHTML = '';
    Object.keys(store).forEach(name => {
        const opt = document.createElement('option');
        opt.value = name;
        opt.textContent = name;
        select.appendChild(opt);
    });
}
function saveWordlist() {
    const name = document.getElementById('wordlist-name').value.trim();
    const content = document.getElementById('wordlist').value.trim();
    if (!name) { alert('请输入词表名称'); return; }
    if (!content) { alert('词表内容不能为空'); return; }
    const store = getWordlistStore();
    store[name] = content;
    setWordlistStore(store);
    refreshWordlistSelect();
    alert('词表已保存');
}
function loadWordlist() {
    const select = document.getElementById('wordlist-select');
    const name = select.value;
    if (!name) return;
    const store = getWordlistStore();
    document.getElementById('wordlist-name').value = name;
    document.getElementById('wordlist').value = store[name] || '';
}
function deleteWordlist() {
    const select = document.getElementById('wordlist-select');
    const name = select.value;
    if (!name) return;
    if (!confirm('确定要删除词表"' + name + '"吗？')) return;
    const store = getWordlistStore();
    delete store[name];
    setWordlistStore(store);
    refreshWordlistSelect();
    document.getElementById('wordlist-name').value = '';
    document.getElementById('wordlist').value = '';
}
function newWordlist() {
    document.getElementById('wordlist-name').value = '';
    document.getElementById('wordlist').value = '';
}
function exportWordlists() {
    const store = getWordlistStore();
    const dataStr = JSON.stringify(store, null, 2);
    const blob = new Blob([dataStr], {type: 'application/json'});
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'wordlists.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}
function importWordlists(event) {
    const file = event.target.files[0];
    if (!file) return;
    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const obj = JSON.parse(e.target.result);
            if (typeof obj !== 'object' || Array.isArray(obj)) throw new Error();
            setWordlistStore(obj);
            refreshWordlistSelect();
            alert('词表导入成功！');
        } catch {
            alert('导入失败，文件格式不正确！');
        }
    };
    reader.readAsText(file);
    event.target.value = '';
}

function startChoiceQuiz() {
    const list = document.getElementById('wordlist').value.trim().split('\n');
    choiceWords = [];
    for (let line of list) {
        let cn, en;
        if (line.includes(',')) {
            [cn, en] = line.split(',');
        } else {
            const idx = line.indexOf(' ');
            if (idx > 0) {
                cn = line.slice(0, idx);
                en = line.slice(idx + 1);
            }
        }
        if (cn && en) choiceWords.push({cn: cn.trim(), en: en.trim()});
    }
    if (choiceWords.length < 4) {
        alert('请至少输入4组单词！');
        return;
    }
    choiceUsed = [];
    choiceCorrect = 0;
    choiceWrong = 0;
    choiceTotal = 0;
    document.getElementById('setup').style.display = 'none';
    document.getElementById('quiz').style.display = 'none';
    document.getElementById('choice-quiz').style.display = '';
    nextChoice();
}
function nextChoice() {
    document.getElementById('choice-result').textContent = '';
    document.getElementById('choiceNextBtn').style.display = 'none';
    if (choiceUsed.length === choiceWords.length) {
        document.getElementById('choice-question').textContent = '全部完成！';
        document.getElementById('choice-options').innerHTML = '';
        document.getElementById('choice-score').textContent = `总共${choiceTotal}题，答对${choiceCorrect}题，答错${choiceWrong}题。`;
        return;
    }
    let idx;
    do {
        idx = Math.floor(Math.random() * choiceWords.length);
    } while (choiceUsed.includes(idx));
    choiceUsed.push(idx);
    choiceCurrent = choiceWords[idx];
    // 生成4个选项
    let options = [choiceCurrent.en];
    while (options.length < 4) {
        let r = Math.floor(Math.random() * choiceWords.length);
        let en = choiceWords[r].en;
        if (!options.includes(en)) options.push(en);
    }
    shuffle(options);
    // 渲染
    document.getElementById('choice-question').textContent = `中文：${choiceCurrent.cn}`;
    const optDiv = document.getElementById('choice-options');
    optDiv.innerHTML = '';
    options.forEach(opt => {
        const btn = document.createElement('button');
        btn.textContent = opt;
        btn.style.fontSize = '1em';
        btn.onclick = () => checkChoice(opt, btn);
        optDiv.appendChild(btn);
    });
    document.getElementById('choice-score').textContent = `已答${choiceTotal}题，答对${choiceCorrect}题，答错${choiceWrong}题。`;
}
function checkChoice(ans, btn) {
    choiceTotal++;
    const allBtns = document.querySelectorAll('#choice-options button');
    allBtns.forEach(b => b.disabled = true);
    if (ans === choiceCurrent.en) {
        choiceCorrect++;
        btn.style.background = '#b2dfdb';
        document.getElementById('choice-result').textContent = '答对了！';
        document.getElementById('choice-result').style.color = 'green';
    } else {
        choiceWrong++;
        btn.style.background = '#ffcdd2';
        document.getElementById('choice-result').textContent = `答错了，正确答案是：${choiceCurrent.en}`;
        document.getElementById('choice-result').style.color = 'red';
    }
    document.getElementById('choice-score').textContent = `已答${choiceTotal}题，答对${choiceCorrect}题，答错${choiceWrong}题。`;
    document.getElementById('choiceNextBtn').style.display = '';
}

window.onload = function() {
    refreshWordlistSelect();
    const select = document.getElementById('wordlist-select');
    if (select.options.length > 0) {
        select.selectedIndex = 0;
        loadWordlist();
    }
};
</script>
</body>
</html> 